import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Share,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Users, Plus, LogIn, Share2, RefreshCw } from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

interface RoomConnectionProps {
  onRoomJoined: (roomId: string) => void;
}

export default function RoomConnection({ onRoomJoined }: RoomConnectionProps) {
  const [playerName, setPlayerName] = useState('');
  const [roomId, setRoomId] = useState('');
  const [mode, setMode] = useState<'menu' | 'create' | 'join'>('menu');
  
  const { createRoom, joinRoom, isConnecting, error, getRoomInfo } = useGameRoom();

  const handleCreateRoom = async () => {
    if (!playerName.trim()) {
      Alert.alert('Atenção', 'Por favor, digite seu nome!');
      return;
    }

    if (playerName.trim().length < 2) {
      Alert.alert('Atenção', 'O nome deve ter pelo menos 2 caracteres!');
      return;
    }

    try {
      const newRoomId = await createRoom(playerName.trim());
      
      // Compartilhar link da sala
      const shareMessage = `🎮 Venha brincar comigo!\n\n` +
        `Sala: ${newRoomId}\n` +
        `Entre no app e use este código para nos encontrarmos!\n\n` +
        `Vamos jogar juntos! 🎉`;
      
      try {
        await Share.share({
          message: shareMessage,
          title: 'Convite para Brincar!',
        });
      } catch (shareError) {
        // Se o compartilhamento falhar, apenas mostrar o código
        Alert.alert(
          'Sala Criada!',
          `Código da sala: ${newRoomId}\n\nCompartilhe este código com seus amigos!`
        );
      }
      
      onRoomJoined(newRoomId);
    } catch (err) {
      Alert.alert('Erro', 'Não foi possível criar a sala. Tente novamente.');
    }
  };

  const handleJoinRoom = async () => {
    if (!playerName.trim()) {
      Alert.alert('Atenção', 'Por favor, digite seu nome!');
      return;
    }

    if (playerName.trim().length < 2) {
      Alert.alert('Atenção', 'O nome deve ter pelo menos 2 caracteres!');
      return;
    }
    
    if (!roomId.trim()) {
      Alert.alert('Atenção', 'Por favor, digite o código da sala!');
      return;
    }

    if (roomId.trim().length !== 6) {
      Alert.alert('Atenção', 'O código da sala deve ter 6 caracteres!');
      return;
    }

    try {
      await joinRoom(roomId.trim().toUpperCase(), playerName.trim());
      onRoomJoined(roomId.trim().toUpperCase());
    } catch (err) {
      // Mostrar erro mais específico baseado na mensagem de erro
      const errorMessage = error || (err instanceof Error ? err.message : 'Erro desconhecido');
      
      if (errorMessage.includes('não encontrada')) {
        Alert.alert(
          'Sala não encontrada', 
          'Verifique se o código está correto ou peça para criarem uma nova sala.'
        );
      } else if (errorMessage.includes('cheia')) {
        Alert.alert(
          'Sala cheia', 
          'Esta sala já tem o máximo de jogadores. Peça para criarem uma nova sala ou tente outro código.'
        );
      } else {
        Alert.alert('Erro', errorMessage);
      }
    }
  };

  const checkRoomStatus = () => {
    if (!roomId.trim()) {
      Alert.alert('Atenção', 'Digite o código da sala primeiro!');
      return;
    }

    const room = getRoomInfo(roomId.trim().toUpperCase());
    if (room) {
      const hostName = room.players.find(p => p.isHost)?.name || 'Desconhecido';
      const playerNames = room.players.map(p => p.name).join(', ');
      
      Alert.alert(
        'Status da Sala',
        `Sala: ${room.id}\n` +
        `Jogadores: ${room.players.length}/4\n` +
        `Criada por: ${hostName}\n` +
        `Participantes: ${playerNames}\n` +
        `Status: ${room.isActive ? 'Ativa' : 'Inativa'}`
      );
    } else {
      Alert.alert('Sala não encontrada', 'Esta sala não existe ou foi removida.');
    }
  };

  const resetForm = () => {
    setPlayerName('');
    setRoomId('');
  };

  if (mode === 'menu') {
    return (
      <LinearGradient
        colors={[COLORS.primary, COLORS.success]}
        style={styles.container}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Users size={80} color="white" />
            <Text style={styles.title}>Sala de Jogos</Text>
            <Text style={styles.subtitle}>Brinque junto com sua família!</Text>
            <Text style={styles.description}>
              Até 4 pessoas podem jogar na mesma sala!
            </Text>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={() => {
                resetForm();
                setMode('create');
              }}
            >
              <Plus size={24} color="white" />
              <Text style={styles.buttonText}>Criar Nova Sala</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={() => {
                resetForm();
                setMode('join');
              }}
            >
              <LogIn size={24} color={COLORS.secondary} />
              <Text style={[styles.buttonText, { color: COLORS.secondary }]}>
                Entrar em uma Sala
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.helpContainer}>
            <Text style={styles.helpTitle}>Como funciona:</Text>
            <Text style={styles.helpText}>
              • Uma pessoa cria a sala e compartilha o código{'\n'}
              • As outras pessoas entram usando o código{'\n'}
              • Todos podem jogar juntos em tempo real!
            </Text>
          </View>
        </View>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={[COLORS.accent, '#FFD93D']}
      style={styles.container}
    >
      <View style={styles.content}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => setMode('menu')}
        >
          <Text style={styles.backButtonText}>← Voltar</Text>
        </TouchableOpacity>
        
        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>
            {mode === 'create' ? 'Criar Nova Sala' : 'Entrar na Sala'}
          </Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Seu Nome</Text>
            <TextInput
              style={styles.input}
              value={playerName}
              onChangeText={setPlayerName}
              placeholder="Como você se chama?"
              placeholderTextColor="#7F8C8D"
              maxLength={20}
              autoCapitalize="words"
            />
          </View>
          
          {mode === 'join' && (
            <>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Código da Sala</Text>
                <View style={styles.inputRow}>
                  <TextInput
                    style={[styles.input, styles.inputWithButton]}
                    value={roomId}
                    onChangeText={(text) => setRoomId(text.toUpperCase())}
                    placeholder="Digite o código"
                    placeholderTextColor="#7F8C8D"
                    autoCapitalize="characters"
                    maxLength={6}
                  />
                  <TouchableOpacity
                    style={styles.checkButton}
                    onPress={checkRoomStatus}
                  >
                    <RefreshCw size={20} color={COLORS.primary} />
                  </TouchableOpacity>
                </View>
              </View>
            </>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, isConnecting && styles.disabledButton]}
            onPress={mode === 'create' ? handleCreateRoom : handleJoinRoom}
            disabled={isConnecting}
          >
            {mode === 'create' ? (
              <Share2 size={24} color="white" />
            ) : (
              <LogIn size={24} color="white" />
            )}
            <Text style={styles.actionButtonText}>
              {isConnecting ? 'Conectando...' : 
               mode === 'create' ? 'Criar e Compartilhar' : 'Entrar na Sala'}
            </Text>
          </TouchableOpacity>

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {mode === 'join' && (
            <View style={styles.troubleshootContainer}>
              <Text style={styles.troubleshootTitle}>Problemas para entrar?</Text>
              <Text style={styles.troubleshootText}>
                • Verifique se o código está correto{'\n'}
                • A sala pode estar cheia (máx. 4 jogadores){'\n'}
                • Peça para criarem uma nova sala
              </Text>
            </View>
          )}
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 50,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    marginTop: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    opacity: 0.9,
    marginTop: 10,
  },
  description: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
    opacity: 0.8,
    marginTop: 5,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300,
    marginBottom: 30,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 25,
    marginBottom: 15,
  },
  primaryButton: {
    backgroundColor: COLORS.secondary,
  },
  secondaryButton: {
    backgroundColor: 'white',
  },
  buttonText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 10,
  },
  helpContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 20,
    maxWidth: 300,
  },
  helpTitle: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 10,
    textAlign: 'center',
  },
  helpText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    lineHeight: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: COLORS.text,
  },
  formContainer: {
    width: '100%',
    maxWidth: 300,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  formTitle: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 25,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: COLORS.text,
    marginBottom: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    borderWidth: 2,
    borderColor: COLORS.primary,
    borderRadius: 15,
    padding: 15,
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    backgroundColor: '#F8F9FA',
  },
  inputWithButton: {
    flex: 1,
    marginRight: 10,
  },
  checkButton: {
    backgroundColor: '#F8F9FA',
    borderWidth: 2,
    borderColor: COLORS.primary,
    borderRadius: 15,
    padding: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.secondary,
    padding: 16,
    borderRadius: 25,
    marginTop: 10,
  },
  disabledButton: {
    opacity: 0.6,
  },
  actionButtonText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 10,
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    borderRadius: 10,
    padding: 15,
    marginTop: 15,
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: '#C62828',
    textAlign: 'center',
  },
  troubleshootContainer: {
    backgroundColor: '#FFF3CD',
    borderRadius: 10,
    padding: 15,
    marginTop: 15,
  },
  troubleshootTitle: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: '#856404',
    marginBottom: 8,
    textAlign: 'center',
  },
  troubleshootText: {
    fontSize: 12,
    fontFamily: 'ComicNeue-Regular',
    color: '#856404',
    textAlign: 'center',
    lineHeight: 16,
  },
});