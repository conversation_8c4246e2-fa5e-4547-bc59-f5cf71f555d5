import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { 
  Camera, 
  CameraOff, 
  Mic, 
  MicOff, 
  RotateCcw, 
  Users,
  Video,
  VideoOff
} from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

export default function VideoChatScreen() {
  const { currentRoom } = useGameRoom();
  const [facing, setFacing] = useState<CameraType>('front');
  const [permission, requestPermission] = useCameraPermissions();
  const [isCameraActive, setIsCameraActive] = useState(true);
  const [isMicActive, setIsMicActive] = useState(true);
  const [isVideoMode, setIsVideoMode] = useState(true);

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const toggleCamera = () => {
    setIsCameraActive(!isCameraActive);
  };

  const toggleMic = () => {
    if (Platform.OS !== 'web') {
      // Implementar controle de microfone nativo
      setIsMicActive(!isMicActive);
    } else {
      Alert.alert(
        'Microfone',
        'Controle de microfone não disponível na versão web. Use as configurações do seu navegador.'
      );
    }
  };

  const toggleVideoMode = () => {
    setIsVideoMode(!isVideoMode);
  };

  if (!currentRoom) {
    return (
      <View style={styles.noRoomContainer}>
        <Users size={80} color={COLORS.primary} />
        <Text style={styles.noRoomText}>
          Você precisa estar em uma sala para usar a câmera!
        </Text>
        <Text style={styles.noRoomSubtext}>
          Vá para a tela inicial e crie ou entre em uma sala.
        </Text>
      </View>
    );
  }

  if (!permission) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Carregando câmera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <LinearGradient
        colors={[COLORS.secondary, '#C44569']}
        style={styles.container}
      >
        <View style={styles.permissionContainer}>
          <Camera size={80} color="white" />
          <Text style={styles.permissionTitle}>Permissão da Câmera</Text>
          <Text style={styles.permissionText}>
            Precisamos da sua permissão para usar a câmera e vocês se verem durante os jogos!
          </Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={requestPermission}
          >
            <Text style={styles.permissionButtonText}>Permitir Câmera</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Câmera e Áudio</Text>
        <View style={styles.roomInfo}>
          <Text style={styles.roomText}>
            Sala: {currentRoom.id} • {currentRoom.players.map(p => p.name).join(' e ')}
          </Text>
        </View>
      </View>

      <View style={styles.cameraContainer}>
        {isCameraActive && isVideoMode ? (
          <CameraView 
            style={styles.camera} 
            facing={facing}
          >
            <View style={styles.cameraOverlay}>
              <Text style={styles.cameraLabel}>Você</Text>
            </View>
          </CameraView>
        ) : (
          <View style={styles.cameraPlaceholder}>
            {isCameraActive ? (
              <>
                <Mic size={60} color="white" />
                <Text style={styles.placeholderText}>Modo Áudio</Text>
                <Text style={styles.placeholderSubtext}>
                  Vocês podem se ouvir mesmo sem vídeo
                </Text>
              </>
            ) : (
              <>
                <CameraOff size={60} color="white" />
                <Text style={styles.placeholderText}>Câmera Desligada</Text>
                <Text style={styles.placeholderSubtext}>
                  Toque no botão para religar
                </Text>
              </>
            )}
          </View>
        )}
      </View>

      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.controlButton, !isCameraActive && styles.inactiveButton]}
          onPress={toggleCamera}
        >
          {isCameraActive ? (
            <Camera size={28} color="white" />
          ) : (
            <CameraOff size={28} color="white" />
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, !isMicActive && styles.inactiveButton]}
          onPress={toggleMic}
        >
          {isMicActive ? (
            <Mic size={28} color="white" />
          ) : (
            <MicOff size={28} color="white" />
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, !isVideoMode && styles.inactiveButton]}
          onPress={toggleVideoMode}
        >
          {isVideoMode ? (
            <Video size={28} color="white" />
          ) : (
            <VideoOff size={28} color="white" />
          )}
        </TouchableOpacity>

        {isCameraActive && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={toggleCameraFacing}
          >
            <RotateCcw size={28} color="white" />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Dicas de Uso:</Text>
        <Text style={styles.instructionsText}>
          • Use a câmera frontal para se verem durante os jogos{'\n'}
          • O áudio funciona mesmo com a câmera desligada{'\n'}
          • Toque nos botões para controlar câmera e microfone{'\n'}
          • Divirtam-se jogando e conversando! 😊
        </Text>
      </View>

      <View style={styles.partnerContainer}>
        <View style={styles.partnerPlaceholder}>
          <Users size={40} color="white" />
          <Text style={styles.partnerText}>
            {currentRoom.players.length > 1 
              ? `${currentRoom.players.find(p => !p.isHost)?.name} também está na sala!`
              : 'Aguardando o outro jogador...'}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  noRoomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  noRoomText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  noRoomSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  permissionTitle: {
    fontSize: 28,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 15,
  },
  permissionText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 26,
  },
  permissionButton: {
    backgroundColor: 'white',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  permissionButtonText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: COLORS.secondary,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  roomInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 10,
  },
  roomText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
  },
  cameraContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 10,
    padding: 8,
  },
  cameraLabel: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
  },
  cameraPlaceholder: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  placeholderText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  placeholderSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    opacity: 0.8,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  controlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 30,
    padding: 15,
    marginHorizontal: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  inactiveButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    margin: 20,
    padding: 15,
  },
  instructionsTitle: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    lineHeight: 18,
  },
  partnerContainer: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  partnerPlaceholder: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  partnerText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 15,
    flex: 1,
    textAlign: 'center',
  },
});