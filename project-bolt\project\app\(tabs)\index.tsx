import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Heart, 
  Star, 
  Gamepad2, 
  <PERSON>, 
  Trophy,
  Sparkles,
  UserMinus,
  Crown,
  Copy,
} from 'lucide-react-native';
import { Share } from 'react-native';
import RoomConnection from '@/components/RoomConnection';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

export default function HomeScreen() {
  const { 
    currentRoom, 
    leaveRoom, 
    getCurrentPlayer, 
    isCurrentPlayerHost, 
    kickPlayer 
  } = useGameRoom();
  const [roomId, setRoomId] = useState<string | null>(null);

  const handleRoomJoined = (newRoomId: string) => {
    setRoomId(newRoomId);
  };

  const handleLeaveRoom = () => {
    Alert.alert(
      'Sair da Sala',
      'Tem certeza que deseja sair da sala?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Sair', 
          style: 'destructive',
          onPress: () => {
            leaveRoom();
            setRoomId(null);
          }
        }
      ]
    );
  };

  const handleShareRoom = async () => {
    if (!currentRoom) return;

    const shareMessage = `🎮 Venha brincar comigo!\n\n` +
      `Sala: ${currentRoom.id}\n` +
      `Entre no app e use este código para nos encontrarmos!\n\n` +
      `Vamos jogar juntos! 🎉`;
    
    try {
      await Share.share({
        message: shareMessage,
        title: 'Convite para Brincar!',
      });
    } catch (error) {
      Alert.alert(
        'Código da Sala',
        `Compartilhe este código: ${currentRoom.id}`
      );
    }
  };

  const handleKickPlayer = (playerId: string, playerName: string) => {
    if (!isCurrentPlayerHost()) {
      Alert.alert('Erro', 'Apenas o criador da sala pode remover jogadores.');
      return;
    }

    Alert.alert(
      'Remover Jogador',
      `Tem certeza que deseja remover ${playerName} da sala?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Remover', 
          style: 'destructive',
          onPress: () => kickPlayer(playerId)
        }
      ]
    );
  };

  if (!currentRoom) {
    return <RoomConnection onRoomJoined={handleRoomJoined} />;
  }

  const currentPlayer = getCurrentPlayer();
  const isHost = isCurrentPlayerHost();

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <View style={styles.roomInfo}>
            <View style={styles.roomIdContainer}>
              <Text style={styles.roomLabel}>Código da Sala:</Text>
              <View style={styles.roomIdRow}>
                <Text style={styles.roomId}>{currentRoom.id}</Text>
                <TouchableOpacity
                  style={styles.copyButton}
                  onPress={handleShareRoom}
                >
                  <Copy size={16} color="white" />
                </TouchableOpacity>
              </View>
              <Text style={styles.playersCount}>
                {currentRoom.players.length}/4 jogadores
              </Text>
            </View>
            <TouchableOpacity
              style={styles.leaveButton}
              onPress={handleLeaveRoom}
            >
              <Text style={styles.leaveButtonText}>Sair</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.welcomeContainer}>
            <Sparkles size={40} color={COLORS.accent} />
            <Text style={styles.welcomeText}>
              Bem-vindos à Sala de Jogos!
            </Text>
            <Heart size={30} color={COLORS.secondary} />
          </View>

          <View style={styles.playersContainer}>
            <Users size={24} color="white" />
            <View style={styles.playersInfo}>
              <Text style={styles.playersText}>
                Jogadores conectados:
              </Text>
              {currentRoom.players.map((player, index) => (
                <View key={player.id} style={styles.playerItem}>
                  <View style={styles.playerInfo}>
                    <Text style={styles.playerName}>
                      {player.name}
                    </Text>
                    <View style={styles.playerBadges}>
                      {player.isHost && <Crown size={16} color={COLORS.accent} />}
                      {player.id === currentPlayer?.id && (
                        <Text style={styles.youBadge}>Você</Text>
                      )}
                    </View>
                  </View>
                  {isHost && !player.isHost && player.id !== currentPlayer?.id && (
                    <TouchableOpacity
                      style={styles.kickButton}
                      onPress={() => handleKickPlayer(player.id, player.name)}
                    >
                      <UserMinus size={16} color={COLORS.secondary} />
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>
        </View>

        <View style={styles.gamesGrid}>
          <Text style={styles.gamesTitle}>Escolha um Jogo!</Text>
          
          <View style={styles.gameCards}>
            <TouchableOpacity style={[styles.gameCard, { backgroundColor: COLORS.primary }]}>
              <Star size={40} color="white" />
              <Text style={styles.gameCardTitle}>Jogo da Velha</Text>
              <Text style={styles.gameCardDescription}>
                Clássico para 2 jogadores
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.gameCard, { backgroundColor: COLORS.secondary }]}>
              <Gamepad2 size={40} color="white" />
              <Text style={styles.gameCardTitle}>Jogo da Memória</Text>
              <Text style={styles.gameCardDescription}>
                Encontre os pares juntos
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.gameCard, { backgroundColor: COLORS.accent }]}>
              <Trophy size={40} color="white" />
              <Text style={styles.gameCardTitle}>Caça Palavras</Text>
              <Text style={styles.gameCardDescription}>
                Aprenda brincando
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.gameCard, { backgroundColor: COLORS.success }]}>
              <Star size={40} color="white" />
              <Text style={styles.gameCardTitle}>Continhas</Text>
              <Text style={styles.gameCardDescription}>
                Matemática divertida
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.gameCard, { backgroundColor: '#E67E22' }]}>
              <Gamepad2 size={40} color="white" />
              <Text style={styles.gameCardTitle}>Desenho Livre</Text>
              <Text style={styles.gameCardDescription}>
                Criem juntos!
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.gameCard, { backgroundColor: '#9B59B6' }]}>
              <Trophy size={40} color="white" />
              <Text style={styles.gameCardTitle}>Pintura</Text>
              <Text style={styles.gameCardDescription}>
                Arte colorida
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>Como Jogar:</Text>
          <Text style={styles.instructionsText}>
            1. Escolha um jogo nas abas do menu
            {'\n'}2. Todos podem ver as jogadas em tempo real
            {'\n'}3. Use a câmera para se verem e conversarem
            {'\n'}4. Até 4 pessoas podem jogar na mesma sala!
            {'\n'}5. Divirtam-se juntos! 💕
          </Text>
        </View>

        {currentRoom.players.length < 4 && (
          <View style={styles.inviteContainer}>
            <Text style={styles.inviteTitle}>Convidem mais pessoas!</Text>
            <Text style={styles.inviteText}>
              Compartilhem o código da sala: <Text style={styles.inviteCode}>{currentRoom.id}</Text>
            </Text>
            <TouchableOpacity
              style={styles.shareButton}
              onPress={handleShareRoom}
            >
              <Text style={styles.shareButtonText}>Compartilhar Convite</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 60,
  },
  header: {
    marginBottom: 30,
  },
  roomInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
  },
  roomIdContainer: {
    flex: 1,
  },
  roomLabel: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.8,
  },
  roomIdRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roomId: {
    fontSize: 20,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    marginRight: 10,
  },
  copyButton: {
    padding: 5,
  },
  playersCount: {
    fontSize: 12,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.7,
  },
  leaveButton: {
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  leaveButtonText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
  },
  welcomeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 15,
  },
  welcomeText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginHorizontal: 15,
  },
  playersContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 15,
  },
  playersInfo: {
    marginLeft: 10,
    flex: 1,
  },
  playersText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 8,
  },
  playerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    padding: 10,
    marginBottom: 5,
  },
  playerInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  playerName: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
  },
  playerBadges: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  youBadge: {
    fontSize: 12,
    fontFamily: 'ComicNeue-Bold',
    color: COLORS.accent,
    marginLeft: 5,
  },
  kickButton: {
    padding: 5,
    marginLeft: 10,
  },
  gamesGrid: {
    marginBottom: 30,
  },
  gamesTitle: {
    fontSize: 28,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  gameCards: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gameCard: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 20,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  gameCardTitle: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 5,
  },
  gameCardDescription: {
    fontSize: 12,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    opacity: 0.9,
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 20,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    lineHeight: 24,
  },
  inviteContainer: {
    backgroundColor: 'rgba(255, 230, 102, 0.3)',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
  },
  inviteTitle: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 10,
  },
  inviteText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 15,
  },
  inviteCode: {
    fontSize: 18,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.accent,
  },
  shareButton: {
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  shareButtonText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
  },
});