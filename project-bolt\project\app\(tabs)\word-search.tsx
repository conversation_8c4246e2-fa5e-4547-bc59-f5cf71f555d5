import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RotateCcw, Trophy, Users, Star } from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

const WORD_LISTS = {
  easy: {
    title: 'Animais',
    words: ['GATO', 'CAO', 'PATO', 'URSO', 'PEIXE'],
    gridSize: 8,
  },
  medium: {
    title: 'Frutas',
    words: ['BANANA', 'MACA', 'UVA', 'LARANJA', 'PERA', 'LIMAO'],
    gridSize: 10,
  },
  hard: {
    title: 'Profissõ<PERSON>',
    words: ['MEDICO', 'PROFESSOR', 'ENGENHEIRO', 'ARTISTA', 'CHEF', 'PILOTO', 'DENTISTA'],
    gridSize: 12,
  },
};

type Difficulty = 'easy' | 'medium' | 'hard';

export default function WordSearchScreen() {
  const { currentRoom } = useGameRoom();
  const [difficulty, setDifficulty] = useState<Difficulty>('easy');
  const [grid, setGrid] = useState<string[][]>([]);
  const [foundWords, setFoundWords] = useState<string[]>([]);
  const [selectedCells, setSelectedCells] = useState<{row: number, col: number}[]>([]);
  const [score, setScore] = useState(0);
  const [isSelecting, setIsSelecting] = useState(false);

  useEffect(() => {
    generateGrid();
  }, [difficulty]);

  const generateGrid = () => {
    const config = WORD_LISTS[difficulty];
    const size = config.gridSize;
    const newGrid: string[][] = Array(size).fill(null).map(() => Array(size).fill(''));
    
    // Fill with random letters first
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        newGrid[i][j] = String.fromCharCode(65 + Math.floor(Math.random() * 26));
      }
    }

    // Place words
    config.words.forEach(word => {
      placeWordInGrid(newGrid, word, size);
    });

    setGrid(newGrid);
    setFoundWords([]);
    setScore(0);
    setSelectedCells([]);
  };

  const placeWordInGrid = (grid: string[][], word: string, size: number) => {
    const directions = [
      [0, 1],   // horizontal
      [1, 0],   // vertical
      [1, 1],   // diagonal down-right
      [-1, 1],  // diagonal up-right
    ];

    for (let attempts = 0; attempts < 100; attempts++) {
      const direction = directions[Math.floor(Math.random() * directions.length)];
      const startRow = Math.floor(Math.random() * size);
      const startCol = Math.floor(Math.random() * size);

      if (canPlaceWord(grid, word, startRow, startCol, direction, size)) {
        for (let i = 0; i < word.length; i++) {
          const row = startRow + direction[0] * i;
          const col = startCol + direction[1] * i;
          grid[row][col] = word[i];
        }
        break;
      }
    }
  };

  const canPlaceWord = (
    grid: string[][],
    word: string,
    startRow: number,
    startCol: number,
    direction: number[],
    size: number
  ): boolean => {
    for (let i = 0; i < word.length; i++) {
      const row = startRow + direction[0] * i;
      const col = startCol + direction[1] * i;
      
      if (row < 0 || row >= size || col < 0 || col >= size) {
        return false;
      }
    }
    return true;
  };

  const handleCellPress = (row: number, col: number) => {
    if (!isSelecting) {
      setIsSelecting(true);
      setSelectedCells([{ row, col }]);
    } else {
      const newSelection = [...selectedCells, { row, col }];
      setSelectedCells(newSelection);
      
      // Check if selection forms a valid word
      const selectedWord = getSelectedWord(newSelection);
      if (selectedWord && WORD_LISTS[difficulty].words.includes(selectedWord)) {
        if (!foundWords.includes(selectedWord)) {
          setFoundWords([...foundWords, selectedWord]);
          setScore(score + selectedWord.length * 10);
          Alert.alert('🎉 Palavra Encontrada!', `"${selectedWord}" - Parabéns!`);
        }
      }
      
      setIsSelecting(false);
      setSelectedCells([]);
    }
  };

  const getSelectedWord = (cells: {row: number, col: number}[]): string | null => {
    if (cells.length < 2) return null;
    
    return cells.map(cell => grid[cell.row][cell.col]).join('');
  };

  const isCellSelected = (row: number, col: number): boolean => {
    return selectedCells.some(cell => cell.row === row && cell.col === col);
  };

  const changeDifficulty = (newDifficulty: Difficulty) => {
    setDifficulty(newDifficulty);
  };

  if (!currentRoom) {
    return (
      <View style={styles.noRoomContainer}>
        <Users size={80} color={COLORS.primary} />
        <Text style={styles.noRoomText}>
          Você precisa estar em uma sala para jogar!
        </Text>
        <Text style={styles.noRoomSubtext}>
          Vá para a tela inicial e crie ou entre em uma sala.
        </Text>
      </View>
    );
  }

  const config = WORD_LISTS[difficulty];
  const progress = foundWords.length / config.words.length;

  return (
    <LinearGradient
      colors={['#A8E6CF', '#7FDBDA']}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Caça Palavras</Text>
          <View style={styles.playersInfo}>
            <Text style={styles.playersText}>
              {currentRoom.players.map(p => p.name).join(' e ')} procurando palavras!
            </Text>
            <Text style={styles.roomText}>Sala: {currentRoom.id}</Text>
          </View>
        </View>

        <View style={styles.difficultyContainer}>
          <Text style={styles.difficultyTitle}>Nível:</Text>
          <View style={styles.difficultyButtons}>
            {(Object.keys(WORD_LISTS) as Difficulty[]).map(level => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.difficultyButton,
                  difficulty === level && styles.activeDifficultyButton
                ]}
                onPress={() => changeDifficulty(level)}
              >
                <Text style={[
                  styles.difficultyButtonText,
                  difficulty === level && styles.activeDifficultyButtonText
                ]}>
                  {level === 'easy' ? 'Fácil' : level === 'medium' ? 'Médio' : 'Difícil'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Trophy size={20} color="white" />
            <Text style={styles.statText}>Pontos: {score}</Text>
          </View>
          <View style={styles.statItem}>
            <Star size={20} color="white" />
            <Text style={styles.statText}>
              {foundWords.length}/{config.words.length} palavras
            </Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${progress * 100}%` }]} />
          </View>
        </View>

        <Text style={styles.themeTitle}>Tema: {config.title}</Text>

        <View style={styles.gameContainer}>
          <View style={styles.grid}>
            {grid.map((row, rowIndex) => (
              <View key={rowIndex} style={styles.gridRow}>
                {row.map((cell, colIndex) => (
                  <TouchableOpacity
                    key={`${rowIndex}-${colIndex}`}
                    style={[
                      styles.gridCell,
                      isCellSelected(rowIndex, colIndex) && styles.selectedCell,
                    ]}
                    onPress={() => handleCellPress(rowIndex, colIndex)}
                  >
                    <Text style={styles.cellText}>{cell}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </View>

          <View style={styles.wordsContainer}>
            <Text style={styles.wordsTitle}>Palavras para encontrar:</Text>
            <View style={styles.wordsList}>
              {config.words.map(word => (
                <View
                  key={word}
                  style={[
                    styles.wordItem,
                    foundWords.includes(word) && styles.foundWordItem
                  ]}
                >
                  <Text style={[
                    styles.wordText,
                    foundWords.includes(word) && styles.foundWordText
                  ]}>
                    {word}
                  </Text>
                  {foundWords.includes(word) && (
                    <Star size={16} color={COLORS.success} />
                  )}
                </View>
              ))}
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={styles.resetButton}
          onPress={generateGrid}
        >
          <RotateCcw size={24} color="white" />
          <Text style={styles.resetButtonText}>Novo Jogo</Text>
        </TouchableOpacity>
      </ScrollView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 60,
  },
  noRoomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  noRoomText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  noRoomSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  playersInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
  },
  playersText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 5,
  },
  roomText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.8,
  },
  difficultyContainer: {
    marginBottom: 20,
  },
  difficultyTitle: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  difficultyButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  difficultyButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 5,
  },
  activeDifficultyButton: {
    backgroundColor: 'white',
  },
  difficultyButtonText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
  },
  activeDifficultyButtonText: {
    color: COLORS.primary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 10,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 5,
  },
  progressContainer: {
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.accent,
    borderRadius: 4,
  },
  themeTitle: {
    fontSize: 20,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  gameContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  grid: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 5,
    marginBottom: 20,
  },
  gridRow: {
    flexDirection: 'row',
  },
  gridCell: {
    width: 25,
    height: 25,
    backgroundColor: '#ECF0F1',
    margin: 1,
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedCell: {
    backgroundColor: COLORS.accent,
  },
  cellText: {
    fontSize: 12,
    fontFamily: 'ComicNeue-Bold',
    color: COLORS.text,
  },
  wordsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    width: '100%',
  },
  wordsTitle: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  wordsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  wordItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    margin: 5,
  },
  foundWordItem: {
    backgroundColor: COLORS.success,
  },
  wordText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginRight: 5,
  },
  foundWordText: {
    textDecorationLine: 'line-through',
    color: 'white',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginTop: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  resetButtonText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 10,
  },
});