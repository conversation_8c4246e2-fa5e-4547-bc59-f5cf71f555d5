import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { Camera, CameraType } from 'expo-camera';
import { Audio } from 'expo-av';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff,
  Users,
  Maximize,
  Minimize
} from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const { width, height } = Dimensions.get('window');

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

interface VideoChatProps {
  roomId: string;
  isVisible: boolean;
  onClose: () => void;
}

export default function VideoChat({ roomId, isVisible, onClose }: VideoChatProps) {
  const { currentRoom } = useGameRoom();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState(CameraType.front);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isCallActive, setIsCallActive] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const cameraRef = useRef<Camera>(null);

  useEffect(() => {
    (async () => {
      // Solicitar permissões de câmera e microfone
      const cameraStatus = await Camera.requestCameraPermissionsAsync();
      const audioStatus = await Audio.requestPermissionsAsync();
      
      setHasPermission(
        cameraStatus.status === 'granted' && audioStatus.status === 'granted'
      );

      if (audioStatus.status === 'granted') {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
          playThroughEarpieceAndroid: false,
          staysActiveInBackground: true,
        });
      }
    })();
  }, []);

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
  };

  const toggleAudio = () => {
    setIsAudioEnabled(!isAudioEnabled);
  };

  const toggleCamera = () => {
    setCameraType(
      cameraType === CameraType.back ? CameraType.front : CameraType.back
    );
  };

  const startCall = () => {
    if (!hasPermission) {
      Alert.alert(
        'Permissões necessárias',
        'Para usar o chat de vídeo, precisamos de acesso à câmera e microfone.',
        [
          { text: 'Cancelar', style: 'cancel' },
          { 
            text: 'Configurações', 
            onPress: () => {
              // Abrir configurações do app
            }
          }
        ]
      );
      return;
    }

    setIsCallActive(true);
    
    // Aqui você integraria com um serviço de WebRTC como Agora.io
    // Por enquanto, apenas simulamos a chamada
    Alert.alert(
      '📹 Chat de Vídeo Ativo!',
      'Agora vocês podem se ver e conversar!\n\n' +
      'Recursos disponíveis:\n' +
      '• Ligar/desligar câmera\n' +
      '• Ligar/desligar microfone\n' +
      '• Trocar câmera (frente/traseira)\n' +
      '• Tela cheia'
    );
  };

  const endCall = () => {
    setIsCallActive(false);
    Alert.alert('Chamada encerrada', 'O chat de vídeo foi desconectado.');
  };

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Solicitando permissões...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <Video size={60} color={COLORS.primary} />
        <Text style={styles.permissionTitle}>Permissões Necessárias</Text>
        <Text style={styles.permissionText}>
          Para usar o chat de vídeo, precisamos de acesso à câmera e microfone.
        </Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={() => {
            Camera.requestCameraPermissionsAsync();
            Audio.requestPermissionsAsync();
          }}
        >
          <Text style={styles.permissionButtonText}>Permitir Acesso</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!isVisible) return null;

  return (
    <View style={[styles.container, isFullScreen && styles.fullScreenContainer]}>
      <View style={styles.header}>
        <View style={styles.roomInfo}>
          <Users size={20} color="white" />
          <Text style={styles.roomText}>Sala: {roomId}</Text>
          <Text style={styles.playersText}>
            {currentRoom?.players.length || 0} jogadores
          </Text>
        </View>
        
        {!isFullScreen && (
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.videoContainer}>
        {isCallActive && isVideoEnabled ? (
          <Camera
            ref={cameraRef}
            style={styles.camera}
            type={cameraType}
            ratio="16:9"
          />
        ) : (
          <View style={styles.videoPlaceholder}>
            <VideoOff size={60} color="white" />
            <Text style={styles.placeholderText}>
              {isCallActive ? 'Câmera desligada' : 'Toque para iniciar'}
            </Text>
          </View>
        )}

        {/* Área para vídeos dos outros participantes */}
        <View style={styles.participantsContainer}>
          {currentRoom?.players
            .filter(p => p.id !== 'current-player-id') // Filtrar o próprio jogador
            .map((player, index) => (
              <View key={player.id} style={styles.participantVideo}>
                <View style={styles.participantPlaceholder}>
                  <Users size={30} color="white" />
                  <Text style={styles.participantName}>{player.name}</Text>
                </View>
              </View>
            ))}
        </View>
      </View>

      <View style={styles.controls}>
        {!isCallActive ? (
          <TouchableOpacity style={styles.startCallButton} onPress={startCall}>
            <Phone size={24} color="white" />
            <Text style={styles.startCallText}>Iniciar Chat de Vídeo</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.activeControls}>
            <TouchableOpacity
              style={[styles.controlButton, !isVideoEnabled && styles.disabledButton]}
              onPress={toggleVideo}
            >
              {isVideoEnabled ? (
                <Video size={24} color="white" />
              ) : (
                <VideoOff size={24} color="white" />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, !isAudioEnabled && styles.disabledButton]}
              onPress={toggleAudio}
            >
              {isAudioEnabled ? (
                <Mic size={24} color="white" />
              ) : (
                <MicOff size={24} color="white" />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={toggleCamera}
            >
              <Text style={styles.cameraToggleText}>🔄</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={toggleFullScreen}
            >
              {isFullScreen ? (
                <Minimize size={24} color="white" />
              ) : (
                <Maximize size={24} color="white" />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, styles.endCallButton]}
              onPress={endCall}
            >
              <PhoneOff size={24} color="white" />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    width: width * 0.4,
    height: height * 0.3,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  fullScreenContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: width,
    height: height,
    borderRadius: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  roomInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  roomText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'ComicNeue-Bold',
    marginLeft: 5,
  },
  playersText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'ComicNeue-Regular',
    marginLeft: 10,
    opacity: 0.8,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  videoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  placeholderText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    marginTop: 10,
    textAlign: 'center',
  },
  participantsContainer: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  participantVideo: {
    width: 80,
    height: 60,
    marginRight: 5,
    marginBottom: 5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  participantPlaceholder: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantName: {
    color: 'white',
    fontSize: 8,
    fontFamily: 'ComicNeue-Bold',
    marginTop: 2,
  },
  controls: {
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  startCallButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.success,
    padding: 12,
    borderRadius: 20,
  },
  startCallText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'ComicNeue-Bold',
    marginLeft: 8,
  },
  activeControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 10,
    borderRadius: 25,
    width: 45,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: COLORS.secondary,
  },
  endCallButton: {
    backgroundColor: '#E74C3C',
  },
  cameraToggleText: {
    fontSize: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  permissionTitle: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 15,
  },
  permissionText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  permissionButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
  },
});
