import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import { getAuth } from 'firebase/auth';

// Configuração do Firebase - CONFIGURAÇÃO REAL
const firebaseConfig = {
  apiKey: "AIzaSyB4l9TtOHcrjy7vpQF2CZ8JOH-9c9PS4Y8",
  authDomain: "kids-game-room-f4522.firebaseapp.com",
  databaseURL: "https://kids-game-room-f4522-default-rtdb.firebaseio.com",
  projectId: "kids-game-room-f4522",
  storageBucket: "kids-game-room-f4522.firebasestorage.app",
  messagingSenderId: "791747179987",
  appId: "1:791747179987:web:d44cba436853df8ceeab26"
};

// Para desenvolvimento/teste, você pode usar uma configuração de teste
const testConfig = {
  apiKey: "test-api-key",
  authDomain: "kids-game-room-test.firebaseapp.com",
  databaseURL: "https://kids-game-room-test-default-rtdb.firebaseio.com/",
  projectId: "kids-game-room-test",
  storageBucket: "kids-game-room-test.appspot.com",
  messagingSenderId: "123456789",
  appId: "test-app-id"
};

// Usando configuração real do Firebase
const app = initializeApp(firebaseConfig);

export const database = getDatabase(app);
export const auth = getAuth(app);
export default app;
