import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import { getAuth } from 'firebase/auth';

// Configuração do Firebase - substitua pelos seus dados do projeto
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project.firebaseapp.com",
  databaseURL: "https://your-project-default-rtdb.firebaseio.com/",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

// Para desenvolvimento/teste, você pode usar uma configuração de teste
const testConfig = {
  apiKey: "test-api-key",
  authDomain: "kids-game-room-test.firebaseapp.com",
  databaseURL: "https://kids-game-room-test-default-rtdb.firebaseio.com/",
  projectId: "kids-game-room-test",
  storageBucket: "kids-game-room-test.appspot.com",
  messagingSenderId: "123456789",
  appId: "test-app-id"
};

// Use testConfig para desenvolvimento, firebaseConfig para produção
const app = initializeApp(__DEV__ ? testConfig : firebaseConfig);

export const database = getDatabase(app);
export const auth = getAuth(app);
export default app;
