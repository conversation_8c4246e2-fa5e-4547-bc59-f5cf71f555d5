import { database } from '@/config/firebase';
import { ref, push, set, get, onValue, off, remove, update } from 'firebase/database';
import { GameRoom, Player, TicTacToeGame } from '@/types/game';

export class FirebaseGameService {
  private static instance: FirebaseGameService;
  private listeners: { [key: string]: () => void } = {};

  static getInstance(): FirebaseGameService {
    if (!FirebaseGameService.instance) {
      FirebaseGameService.instance = new FirebaseGameService();
    }
    return FirebaseGameService.instance;
  }

  // Criar uma nova sala
  async createRoom(playerName: string): Promise<string> {
    const roomId = this.generateRoomId();
    const playerId = this.generatePlayerId();
    
    const newRoom: GameRoom = {
      id: roomId,
      players: [{
        id: playerId,
        name: playerName,
        isHost: true,
      }],
      currentGame: 'tic-tac-toe',
      isActive: true,
      createdAt: new Date(),
    };

    const roomRef = ref(database, `rooms/${roomId}`);
    await set(roomRef, {
      ...newRoom,
      createdAt: newRoom.createdAt.toISOString(),
    });

    return roomId;
  }

  // Entrar em uma sala existente
  async joinRoom(roomId: string, playerName: string): Promise<string> {
    const roomRef = ref(database, `rooms/${roomId}`);
    const snapshot = await get(roomRef);
    
    if (!snapshot.exists()) {
      throw new Error('Sala não encontrada! Verifique se o código está correto.');
    }

    const room = snapshot.val() as GameRoom;
    
    // Verificar se já existe um jogador com esse nome
    const existingPlayer = room.players.find(p => 
      p.name.toLowerCase() === playerName.toLowerCase()
    );
    
    if (existingPlayer) {
      return existingPlayer.id;
    }

    // Verificar limite de jogadores
    if (room.players.length >= 4) {
      throw new Error('Sala está cheia! Máximo de 4 jogadores por sala.');
    }

    const playerId = this.generatePlayerId();
    const newPlayer: Player = {
      id: playerId,
      name: playerName,
      isHost: false,
    };

    // Adicionar jogador à sala
    const playersRef = ref(database, `rooms/${roomId}/players`);
    const updatedPlayers = [...room.players, newPlayer];
    await set(playersRef, updatedPlayers);

    return playerId;
  }

  // Sair da sala
  async leaveRoom(roomId: string, playerId: string): Promise<void> {
    const roomRef = ref(database, `rooms/${roomId}`);
    const snapshot = await get(roomRef);
    
    if (!snapshot.exists()) return;

    const room = snapshot.val() as GameRoom;
    const player = room.players.find(p => p.id === playerId);
    
    if (!player) return;

    if (player.isHost) {
      // Se for o host, remover a sala completamente
      await remove(roomRef);
    } else {
      // Remover apenas o jogador
      const updatedPlayers = room.players.filter(p => p.id !== playerId);
      
      if (updatedPlayers.length === 0) {
        await remove(roomRef);
      } else {
        // Se não há mais host, promover o primeiro jogador
        if (!updatedPlayers.some(p => p.isHost)) {
          updatedPlayers[0].isHost = true;
        }
        
        const playersRef = ref(database, `rooms/${roomId}/players`);
        await set(playersRef, updatedPlayers);
      }
    }
  }

  // Expulsar jogador (apenas host)
  async kickPlayer(roomId: string, hostId: string, targetPlayerId: string): Promise<void> {
    const roomRef = ref(database, `rooms/${roomId}`);
    const snapshot = await get(roomRef);
    
    if (!snapshot.exists()) return;

    const room = snapshot.val() as GameRoom;
    const host = room.players.find(p => p.id === hostId);
    
    if (!host?.isHost) {
      throw new Error('Apenas o host pode expulsar jogadores');
    }

    const updatedPlayers = room.players.filter(p => p.id !== targetPlayerId);
    const playersRef = ref(database, `rooms/${roomId}/players`);
    await set(playersRef, updatedPlayers);
  }

  // Escutar mudanças na sala
  listenToRoom(roomId: string, callback: (room: GameRoom | null) => void): () => void {
    const roomRef = ref(database, `rooms/${roomId}`);
    
    const unsubscribe = onValue(roomRef, (snapshot) => {
      if (snapshot.exists()) {
        const room = snapshot.val() as GameRoom;
        room.createdAt = new Date(room.createdAt);
        callback(room);
      } else {
        callback(null);
      }
    });

    // Armazenar listener para cleanup
    const listenerId = `room_${roomId}_${Date.now()}`;
    this.listeners[listenerId] = () => off(roomRef, 'value', unsubscribe);
    
    return this.listeners[listenerId];
  }

  // Atualizar estado do jogo
  async updateGameState(roomId: string, gameType: string, gameState: any): Promise<void> {
    const gameRef = ref(database, `rooms/${roomId}/gameStates/${gameType}`);
    await set(gameRef, gameState);
  }

  // Escutar mudanças no estado do jogo
  listenToGameState(roomId: string, gameType: string, callback: (gameState: any) => void): () => void {
    const gameRef = ref(database, `rooms/${roomId}/gameStates/${gameType}`);
    
    const unsubscribe = onValue(gameRef, (snapshot) => {
      if (snapshot.exists()) {
        callback(snapshot.val());
      }
    });

    const listenerId = `game_${roomId}_${gameType}_${Date.now()}`;
    this.listeners[listenerId] = () => off(gameRef, 'value', unsubscribe);
    
    return this.listeners[listenerId];
  }

  // Limpar todos os listeners
  cleanup(): void {
    Object.values(this.listeners).forEach(cleanup => cleanup());
    this.listeners = {};
  }

  private generateRoomId(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  private generatePlayerId(): string {
    return 'player_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
  }
}

export const gameService = FirebaseGameService.getInstance();
