import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Users } from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

const { width, height } = Dimensions.get('window');

export default function PaintingScreen() {
  const { currentRoom } = useGameRoom();

  if (!currentRoom) {
    return (
      <View style={styles.noRoomContainer}>
        <Users size={80} color={COLORS.primary} />
        <Text style={styles.noRoomText}>
          Você precisa estar em uma sala para pintar!
        </Text>
        <Text style={styles.noRoomSubtext}>
          Vá para a tela inicial e crie ou entre em uma sala.
        </Text>
      </View>
    );
  }

  return (
    <LinearGradient
      colors={['#FA8072', '#FFB6C1']}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Pintura Digital</Text>
        <View style={styles.playersInfo}>
          <Text style={styles.playersText}>
            {currentRoom.players.map(p => p.name).join(' e ')} pintando juntos!
          </Text>
          <Text style={styles.roomText}>Sala: {currentRoom.id}</Text>
        </View>
      </View>

      <View style={styles.comingSoonContainer}>
        <Text style={styles.comingSoonTitle}>🎨 Em Breve!</Text>
        <Text style={styles.comingSoonText}>
          A pintura digital estará disponível em breve!{'\n\n'}
          Aqui vocês poderão:{'\n'}
          • Pintar com diferentes pincéis e texturas{'\n'}
          • Usar uma paleta de cores completa{'\n'}
          • Adicionar efeitos especiais{'\n'}
          • Trabalhar no mesmo quadro simultaneamente{'\n'}
          • Criar obras de arte incríveis juntos!
        </Text>
      </View>

      <View style={styles.placeholderCanvas}>
        <Text style={styles.placeholderText}>
          Tela de Pintura{'\n'}(Em desenvolvimento)
        </Text>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  noRoomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  noRoomText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  noRoomSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    
    marginBottom: 30,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  playersInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
  },
  playersText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 5,
  },
  roomText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.8,
  },
  comingSoonContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    padding: 25,
    marginBottom: 20,
  },
  comingSoonTitle: {
    fontSize: 28,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 15,
  },
  comingSoonText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    textAlign: 'center',
    lineHeight: 24,
  },
  placeholderCanvas: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  placeholderText: {
    fontSize: 20,
    fontFamily: 'ComicNeue-Bold',
    color: '#BDC3C7',
    textAlign: 'center',
    lineHeight: 28,
  },
});