{"expo": {"name": "Kids Game Room", "slug": "kids-game-room", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "kidsgameroom", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Este app precisa da câmera para que as crianças possam se ver durante os jogos.", "NSMicrophoneUsageDescription": "Este app precisa do microfone para que as crianças possam conversar durante os jogos."}}, "android": {"package": "com.kidsgameroom.app", "versionCode": 1, "permissions": ["CAMERA", "RECORD_AUDIO", "INTERNET", "ACCESS_NETWORK_STATE"], "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#4ECDC4"}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", ["expo-camera", {"cameraPermission": "Permitir que $(PRODUCT_NAME) acesse sua câmera para chat de vídeo.", "microphonePermission": "Permitir que $(PRODUCT_NAME) acesse seu microfone para chat de áudio.", "recordAudioAndroid": true}]], "experiments": {"typedRoutes": true}}}