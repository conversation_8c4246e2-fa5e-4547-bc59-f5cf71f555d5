# 🔥 Configuração do Firebase - Kids Game Room

## 📋 Passo a Passo para Configurar Firebase

### 1. Criar Projeto no Firebase

1. Acesse [Firebase Console](https://console.firebase.google.com/)
2. Clique em "Adicionar projeto"
3. Nome do projeto: `kids-game-room`
4. Desabilite Google Analytics (opcional para este projeto)
5. Clique em "Criar projeto"

### 2. Configurar Realtime Database

1. No painel do Firebase, vá em **"Realtime Database"**
2. Clique em **"Criar banco de dados"**
3. Escolha **"Começar no modo de teste"** (para desenvolvimento)
4. Selecione a localização mais próxima (ex: `us-central1`)

### 3. Configurar Regras de Segurança (Desenvolvimento)

No Realtime Database, vá em **"Regras"** e substitua **TODO O CONTEÚDO** por:

```javascript
{
  "rules": {
    "rooms": {
      "$roomId": {
        ".read": true,
        ".write": true,
        ".validate": "newData.hasChildren(['id', 'players', 'isActive'])",
        "players": {
          ".validate": "newData.hasChildren() && newData.val().length <= 4"
        }
      }
    }
  }
}
```

**⚠️ IMPORTANTE**:
1. **Apague TUDO** que está no campo de regras
2. Cole exatamente este código, incluindo as chaves `{` e `}`
3. Clique em **"Publicar"**

**🚨 Se der erro "mismatched input"**:
- Certifique-se de apagar TODO o conteúdo anterior
- Cole apenas o código acima
- Não adicione nada antes ou depois

### 4. Obter Configurações do Projeto

1. Vá em **"Configurações do projeto"** (ícone de engrenagem)
2. Na aba **"Geral"**, role até **"Seus apps"**
3. Clique em **"Adicionar app"** > **"Web"** (ícone `</>`
4. Nome do app: `Kids Game Room Web`
5. **NÃO** marque "Configurar Firebase Hosting"
6. Clique em **"Registrar app"**
7. **COPIE** as configurações que aparecem

### 5. Atualizar Arquivo de Configuração

Edite o arquivo `config/firebase.ts` e substitua as configurações:

```typescript
const firebaseConfig = {
  apiKey: "SUA_API_KEY_AQUI",
  authDomain: "kids-game-room-xxxxx.firebaseapp.com",
  databaseURL: "https://kids-game-room-xxxxx-default-rtdb.firebaseio.com/",
  projectId: "kids-game-room-xxxxx",
  storageBucket: "kids-game-room-xxxxx.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef123456789"
};
```

### 6. Testar Conexão

Execute o app e teste:

```bash
npm run start
```

1. Crie uma sala
2. Verifique no Firebase Console se apareceu em **Realtime Database**
3. Entre com outro dispositivo na mesma sala
4. Verifique se ambos jogadores aparecem no banco

### 7. Configurações de Produção

#### Regras de Segurança Mais Restritivas

Para produção, use regras mais seguras:

```javascript
{
  "rules": {
    "rooms": {
      "$roomId": {
        ".read": true,
        ".write": "!data.exists() || data.exists()",
        ".validate": "newData.hasChildren(['id', 'players', 'isActive'])",
        "id": {
          ".validate": "newData.isString() && newData.val().length == 6"
        },
        "players": {
          ".validate": "newData.hasChildren() && newData.val().length <= 4",
          "$playerId": {
            ".validate": "newData.hasChildren(['id', 'name', 'isHost'])",
            "name": {
              ".validate": "newData.isString() && newData.val().length >= 2 && newData.val().length <= 20"
            }
          }
        },
        "gameStates": {
          "$gameType": {
            ".read": true,
            ".write": true
          }
        },
        "createdAt": {
          ".validate": "newData.isString()"
        },
        "isActive": {
          ".validate": "newData.isBoolean()"
        }
      }
    }
  }
}
```

#### Limpeza Automática de Salas Antigas

Configure uma Cloud Function para limpar salas antigas:

```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

exports.cleanupOldRooms = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    const db = admin.database();
    const roomsRef = db.ref('rooms');
    
    const snapshot = await roomsRef.once('value');
    const rooms = snapshot.val();
    
    if (!rooms) return null;
    
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000); // 24 horas
    
    const updates = {};
    
    Object.keys(rooms).forEach(roomId => {
      const room = rooms[roomId];
      const createdAt = new Date(room.createdAt).getTime();
      
      // Remove salas criadas há mais de 24 horas
      if (createdAt < oneDayAgo) {
        updates[roomId] = null;
      }
    });
    
    if (Object.keys(updates).length > 0) {
      await roomsRef.update(updates);
      console.log(`Removed ${Object.keys(updates).length} old rooms`);
    }
    
    return null;
  });
```

### 8. Monitoramento e Analytics

#### Configurar Analytics (Opcional)

1. No Firebase Console, vá em **"Analytics"**
2. Clique em **"Ativar Google Analytics"**
3. Escolha uma conta do Google Analytics ou crie uma nova

#### Métricas Importantes para Monitorar

- Número de salas criadas por dia
- Tempo médio de permanência em salas
- Jogos mais populares
- Erros de conexão
- Performance do Realtime Database

### 9. Backup e Segurança

#### Configurar Backup Automático

1. Vá em **"Realtime Database"** > **"Backup"**
2. Configure backup diário automático
3. Escolha um bucket do Google Cloud Storage

#### Configurar Alertas

1. Vá em **"Alertas"**
2. Configure alertas para:
   - Uso excessivo de bandwidth
   - Muitas operações de escrita
   - Erros de autenticação

### 🚨 Importante para Desenvolvimento

#### Configuração de Teste Rápida

Para testes rápidos, você pode usar estas configurações temporárias:

```typescript
// config/firebase.ts - APENAS PARA DESENVOLVIMENTO
const testConfig = {
  apiKey: "fake-api-key",
  authDomain: "test.firebaseapp.com",
  databaseURL: "https://test-default-rtdb.firebaseio.com/",
  projectId: "test-project",
  storageBucket: "test.appspot.com",
  messagingSenderId: "123456789",
  appId: "test-app-id"
};
```

**⚠️ ATENÇÃO**: Substitua por configurações reais antes de fazer deploy!

### � Solução de Problemas Comuns

#### Erro: "mismatched input '{' expecting..."

**Causa**: O Firebase está esperando um formato diferente de regras.

**Solução**:
1. Vá em **Realtime Database** > **Regras**
2. **APAGUE TUDO** que está no campo
3. Cole exatamente isto (sem adicionar nada):

```javascript
{
  "rules": {
    "rooms": {
      "$roomId": {
        ".read": true,
        ".write": true
      }
    }
  }
}
```

4. Clique em **"Publicar"**

#### Erro: "Permission denied"

**Causa**: As regras estão muito restritivas.

**Solução**: Use as regras de desenvolvimento acima (mais permissivas).

#### Erro: "Firebase not configured"

**Causa**: As configurações no arquivo `config/firebase.ts` estão incorretas.

**Solução**:
1. Copie as configurações corretas do Firebase Console
2. Substitua no arquivo `config/firebase.ts`
3. Reinicie o app

### �📞 Suporte

Se encontrar problemas:

1. Verifique o console do navegador para erros
2. Confira as regras do Realtime Database
3. Teste a conectividade com internet
4. Consulte a [documentação oficial do Firebase](https://firebase.google.com/docs)

### ✅ Checklist de Configuração

- [ ] Projeto Firebase criado
- [ ] Realtime Database configurado
- [ ] Regras de segurança definidas
- [ ] Configurações copiadas para o app
- [ ] Teste de conexão realizado
- [ ] Backup configurado (produção)
- [ ] Monitoramento ativo (produção)
