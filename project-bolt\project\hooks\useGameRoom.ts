import { useState, useEffect, useCallback } from 'react';
import { GameRoom, Player } from '@/types/game';
import { gameService } from '@/services/FirebaseGameService';

let currentPlayerId: string | null = null;

export function useGameRoom(roomId?: string) {
  const [currentRoom, setCurrentRoom] = useState<GameRoom | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [roomListener, setRoomListener] = useState<(() => void) | null>(null);

  const createRoom = useCallback(async (playerName: string): Promise<string> => {
    setIsConnecting(true);
    setError(null);

    try {
      const newRoomId = await gameService.createRoom(playerName);
      currentPlayerId = 'host-' + Date.now();

      // Configurar listener para a sala
      const unsubscribe = gameService.listenToRoom(newRoomId, (room) => {
        setCurrentRoom(room);
      });
      setRoomListener(() => unsubscribe);

      setIsConnecting(false);
      return newRoomId;
    } catch (err) {
      setError('Erro ao criar sala');
      setIsConnecting(false);
      throw err;
    }
  }, []);

  const joinRoom = useCallback(async (roomId: string, playerName: string): Promise<void> => {
    setIsConnecting(true);
    setError(null);

    try {
      const playerId = await gameService.joinRoom(roomId, playerName);
      currentPlayerId = playerId;

      // Configurar listener para a sala
      const unsubscribe = gameService.listenToRoom(roomId, (room) => {
        setCurrentRoom(room);
      });
      setRoomListener(() => unsubscribe);

      setIsConnecting(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao entrar na sala');
      setIsConnecting(false);
      throw err;
    }
  }, []);

  const leaveRoom = useCallback(async () => {
    if (currentRoom && currentPlayerId) {
      try {
        await gameService.leaveRoom(currentRoom.id, currentPlayerId);

        // Limpar listener
        if (roomListener) {
          roomListener();
          setRoomListener(null);
        }

        setCurrentRoom(null);
        currentPlayerId = null;
      } catch (err) {
        console.error('Erro ao sair da sala:', err);
      }
    }
  }, [currentRoom, roomListener]);

  const updateRoom = useCallback((updates: Partial<GameRoom>) => {
    // Esta função agora é gerenciada pelo Firebase automaticamente
    // através dos listeners
  }, []);

  const kickPlayer = useCallback(async (playerId: string) => {
    if (currentRoom && currentPlayerId) {
      try {
        await gameService.kickPlayer(currentRoom.id, currentPlayerId, playerId);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro ao expulsar jogador');
      }
    }
  }, [currentRoom]);

  const getRoomInfo = useCallback((roomId: string) => {
    // Para obter informações da sala, seria necessário fazer uma consulta ao Firebase
    // Por enquanto, retornamos null - pode ser implementado se necessário
    return null;
  }, []);

  const getCurrentPlayer = useCallback(() => {
    if (currentRoom && currentPlayerId) {
      return currentRoom.players.find(p => p.id === currentPlayerId) || null;
    }
    return null;
  }, [currentRoom]);

  const isCurrentPlayerHost = useCallback(() => {
    const player = getCurrentPlayer();
    return player?.isHost || false;
  }, [getCurrentPlayer]);

  // Limpar listeners quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (roomListener) {
        roomListener();
      }
      gameService.cleanup();
    };
  }, [roomListener]);

  return {
    currentRoom,
    isConnecting,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    updateRoom,
    kickPlayer,
    getRoomInfo,
    getCurrentPlayer,
    isCurrentPlayerHost,
    currentPlayerId,
  };
}