import { useState, useEffect, useCallback } from 'react';
import { GameRoom, Player } from '@/types/game';

// Simulação de estado global - em produção, usar WebSocket ou estado global real
let globalGameRooms: { [key: string]: GameRoom } = {};
let currentPlayerId: string | null = null;

export function useGameRoom(roomId?: string) {
  const [currentRoom, setCurrentRoom] = useState<GameRoom | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createRoom = useCallback(async (playerName: string): Promise<string> => {
    setIsConnecting(true);
    setError(null);
    
    try {
      const newRoomId = Math.random().toString(36).substring(2, 8).toUpperCase();
      const playerId = 'host-' + Date.now();
      currentPlayerId = playerId;
      
      const newRoom: GameRoom = {
        id: newRoomId,
        players: [{
          id: playerId,
          name: playerName,
          isHost: true,
        }],
        currentGame: 'tic-tac-toe',
        isActive: true,
        createdAt: new Date(),
      };
      
      globalGameRooms[newRoomId] = newRoom;
      setCurrentRoom(newRoom);
      setIsConnecting(false);
      
      return newRoomId;
    } catch (err) {
      setError('Erro ao criar sala');
      setIsConnecting(false);
      throw err;
    }
  }, []);

  const joinRoom = useCallback(async (roomId: string, playerName: string): Promise<void> => {
    setIsConnecting(true);
    setError(null);
    
    try {
      const room = globalGameRooms[roomId];
      if (!room) {
        throw new Error('Sala não encontrada! Verifique se o código está correto.');
      }
      
      // Verificar se o jogador já está na sala pelo nome
      const existingPlayer = room.players.find(p => p.name.toLowerCase() === playerName.toLowerCase());
      if (existingPlayer) {
        // Se o jogador já existe, apenas reconectar
        currentPlayerId = existingPlayer.id;
        setCurrentRoom(room);
        setIsConnecting(false);
        return;
      }
      
      // Permitir até 4 jogadores na sala
      if (room.players.length >= 4) {
        throw new Error('Sala está cheia! Máximo de 4 jogadores por sala.');
      }
      
      const playerId = 'player-' + Date.now();
      currentPlayerId = playerId;
      
      const updatedRoom = {
        ...room,
        players: [...room.players, {
          id: playerId,
          name: playerName,
          isHost: false,
        }],
      };
      
      globalGameRooms[roomId] = updatedRoom;
      setCurrentRoom(updatedRoom);
      setIsConnecting(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao entrar na sala');
      setIsConnecting(false);
      throw err;
    }
  }, []);

  const leaveRoom = useCallback(() => {
    if (currentRoom && currentPlayerId) {
      const currentPlayer = currentRoom.players.find(p => p.id === currentPlayerId);
      
      if (currentPlayer?.isHost) {
        // Se for o host, remover a sala completamente
        delete globalGameRooms[currentRoom.id];
      } else {
        // Se não for o host, apenas remover o jogador da sala
        const updatedRoom = {
          ...currentRoom,
          players: currentRoom.players.filter(p => p.id !== currentPlayerId)
        };
        
        if (updatedRoom.players.length > 0) {
          // Se ainda há jogadores, promover o primeiro jogador restante como host
          if (!updatedRoom.players.some(p => p.isHost)) {
            updatedRoom.players[0].isHost = true;
          }
          globalGameRooms[currentRoom.id] = updatedRoom;
        } else {
          delete globalGameRooms[currentRoom.id];
        }
      }
      
      setCurrentRoom(null);
      currentPlayerId = null;
    }
  }, [currentRoom]);

  const updateRoom = useCallback((updates: Partial<GameRoom>) => {
    if (currentRoom) {
      const updatedRoom = { ...currentRoom, ...updates };
      globalGameRooms[currentRoom.id] = updatedRoom;
      setCurrentRoom(updatedRoom);
    }
  }, [currentRoom]);

  const kickPlayer = useCallback((playerId: string) => {
    if (currentRoom && currentPlayerId) {
      const currentPlayer = currentRoom.players.find(p => p.id === currentPlayerId);
      
      // Apenas o host pode expulsar jogadores
      if (currentPlayer?.isHost) {
        const updatedRoom = {
          ...currentRoom,
          players: currentRoom.players.filter(p => p.id !== playerId)
        };
        globalGameRooms[currentRoom.id] = updatedRoom;
        setCurrentRoom(updatedRoom);
      }
    }
  }, [currentRoom]);

  const getRoomInfo = useCallback((roomId: string) => {
    return globalGameRooms[roomId] || null;
  }, []);

  const getCurrentPlayer = useCallback(() => {
    if (currentRoom && currentPlayerId) {
      return currentRoom.players.find(p => p.id === currentPlayerId) || null;
    }
    return null;
  }, [currentRoom]);

  const isCurrentPlayerHost = useCallback(() => {
    const player = getCurrentPlayer();
    return player?.isHost || false;
  }, [getCurrentPlayer]);

  // Simular sincronização em tempo real - atualizar a sala atual se houver mudanças
  useEffect(() => {
    if (!currentRoom) return;
    
    const interval = setInterval(() => {
      const room = globalGameRooms[currentRoom.id];
      if (room) {
        // Verificar se ainda estamos na sala
        const stillInRoom = room.players.some(p => p.id === currentPlayerId);
        if (!stillInRoom) {
          // Fomos removidos da sala
          setCurrentRoom(null);
          currentPlayerId = null;
        } else if (JSON.stringify(room) !== JSON.stringify(currentRoom)) {
          // Atualizar com as mudanças da sala
          setCurrentRoom(room);
        }
      } else {
        // A sala foi removida
        setCurrentRoom(null);
        currentPlayerId = null;
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, [currentRoom]);

  // Limpar estado quando o componente for desmontado
  useEffect(() => {
    return () => {
      // Não limpar automaticamente ao desmontar para manter a conexão
    };
  }, []);

  return {
    currentRoom,
    isConnecting,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    updateRoom,
    kickPlayer,
    getRoomInfo,
    getCurrentPlayer,
    isCurrentPlayerHost,
    currentPlayerId,
  };
}