import { Tabs } from 'expo-router';
import { Platform } from 'react-native';
import { Chrome as Home, Grid3x3 as Grid3X3, Brain, Search, Calculator, PaintBucket, Pencil, Video } from 'lucide-react-native';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: COLORS.secondary,
        tabBarInactiveTintColor: '#95A5A6',
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 0,
          elevation: Platform.OS === 'android' ? 8 : 0,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          height: Platform.OS === 'ios' ? 90 : 70,
          paddingBottom: Platform.OS === 'ios' ? 25 : 10,
          paddingTop: 10,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'ComicNeue-Bold',
          marginTop: 4,
        },
        tabBarIconStyle: {
          marginTop: 4,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Início',
          tabBarIcon: ({ size, color }) => (
            <Home size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="tic-tac-toe"
        options={{
          title: 'Jogo da Velha',
          tabBarIcon: ({ size, color }) => (
            <Grid3X3 size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="memory"
        options={{
          title: 'Memória',
          tabBarIcon: ({ size, color }) => (
            <Brain size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="word-search"
        options={{
          title: 'Caça Palavras',
          tabBarIcon: ({ size, color }) => (
            <Search size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="math"
        options={{
          title: 'Continhas',
          tabBarIcon: ({ size, color }) => (
            <Calculator size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="drawing"
        options={{
          title: 'Desenho',
          tabBarIcon: ({ size, color }) => (
            <Pencil size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="painting"
        options={{
          title: 'Pintura',
          tabBarIcon: ({ size, color }) => (
            <PaintBucket size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="video-chat"
        options={{
          title: 'Câmera',
          tabBarIcon: ({ size, color }) => (
            <Video size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}