import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RotateCcw, Trophy, Users } from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

export default function TicTacToeScreen() {
  const { currentRoom } = useGameRoom();
  const [board, setBoard] = useState<Array<string | null>>(Array(9).fill(null));
  const [currentPlayer, setCurrentPlayer] = useState<'X' | 'O'>('X');
  const [winner, setWinner] = useState<string | null>(null);
  const [gameOver, setGameOver] = useState(false);

  const checkWinner = (squares: Array<string | null>) => {
    const lines = [
      [0, 1, 2],
      [3, 4, 5],
      [6, 7, 8],
      [0, 3, 6],
      [1, 4, 7],
      [2, 5, 8],
      [0, 4, 8],
      [2, 4, 6],
    ];

    for (const line of lines) {
      const [a, b, c] = line;
      if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {
        return squares[a];
      }
    }

    return null;
  };

  const handleSquarePress = (index: number) => {
    if (board[index] || winner || gameOver) return;

    const newBoard = [...board];
    newBoard[index] = currentPlayer;
    setBoard(newBoard);

    const gameWinner = checkWinner(newBoard);
    if (gameWinner) {
      setWinner(gameWinner);
      setGameOver(true);
      Alert.alert(
        '🎉 Parabéns!', 
        `Jogador ${gameWinner} ganhou!`,
        [{ text: 'Jogar Novamente', onPress: resetGame }]
      );
    } else if (newBoard.every(square => square !== null)) {
      setGameOver(true);
      Alert.alert(
        '😊 Empate!', 
        'Que jogo incrível! Vamos tentar novamente?',
        [{ text: 'Jogar Novamente', onPress: resetGame }]
      );
    } else {
      setCurrentPlayer(currentPlayer === 'X' ? 'O' : 'X');
    }
  };

  const resetGame = () => {
    setBoard(Array(9).fill(null));
    setCurrentPlayer('X');
    setWinner(null);
    setGameOver(false);
  };

  const renderSquare = (index: number) => {
    const value = board[index];
    const isWinningSquare = winner && checkWinningSquares().includes(index);
    
    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.square,
          isWinningSquare && styles.winningSquare,
        ]}
        onPress={() => handleSquarePress(index)}
      >
        <Text style={[
          styles.squareText,
          value === 'X' && styles.xText,
          value === 'O' && styles.oText,
        ]}>
          {value}
        </Text>
      </TouchableOpacity>
    );
  };

  const checkWinningSquares = () => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8],
      [0, 3, 6], [1, 4, 7], [2, 5, 8],
      [0, 4, 8], [2, 4, 6],
    ];

    for (const line of lines) {
      const [a, b, c] = line;
      if (board[a] && board[a] === board[b] && board[a] === board[c]) {
        return line;
      }
    }
    return [];
  };

  if (!currentRoom) {
    return (
      <View style={styles.noRoomContainer}>
        <Users size={80} color={COLORS.primary} />
        <Text style={styles.noRoomText}>
          Você precisa estar em uma sala para jogar!
        </Text>
        <Text style={styles.noRoomSubtext}>
          Vá para a tela inicial e crie ou entre em uma sala.
        </Text>
      </View>
    );
  }

  return (
    <LinearGradient
      colors={[COLORS.primary, COLORS.success]}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Jogo da Velha</Text>
        <View style={styles.playersInfo}>
          <Text style={styles.playersText}>
            {currentRoom.players.map(p => p.name).join(' vs ')}
          </Text>
          <Text style={styles.roomText}>Sala: {currentRoom.id}</Text>
        </View>
      </View>

      <View style={styles.gameContainer}>
        <View style={styles.statusContainer}>
          {winner ? (
            <View style={styles.winnerContainer}>
              <Trophy size={32} color={COLORS.accent} />
              <Text style={styles.winnerText}>Jogador {winner} ganhou! 🎉</Text>
            </View>
          ) : gameOver ? (
            <Text style={styles.statusText}>Empate! 🤝</Text>
          ) : (
            <Text style={styles.statusText}>
              Vez do jogador: <Text style={styles.currentPlayerText}>{currentPlayer}</Text>
            </Text>
          )}
        </View>

        <View style={styles.board}>
          {Array(3).fill(null).map((_, row) => (
            <View key={row} style={styles.row}>
              {Array(3).fill(null).map((_, col) => 
                renderSquare(row * 3 + col)
              )}
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetGame}
        >
          <RotateCcw size={24} color="white" />
          <Text style={styles.resetButtonText}>Novo Jogo</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Como Jogar:</Text>
        <Text style={styles.instructionsText}>
          • X sempre começa primeiro{'\n'}
          • Consiga 3 símbolos em linha (horizontal, vertical ou diagonal){'\n'}
          • Vocês dois podem ver as jogadas em tempo real!
        </Text>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  noRoomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  noRoomText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  noRoomSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 36,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  playersInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
  },
  playersText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 5,
  },
  roomText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.8,
  },
  gameContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  winnerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    padding: 15,
  },
  winnerText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    marginLeft: 10,
  },
  statusText: {
    fontSize: 20,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
  },
  currentPlayerText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.accent,
  },
  board: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 10,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  row: {
    flexDirection: 'row',
  },
  square: {
    width: 80,
    height: 80,
    backgroundColor: '#ECF0F1',
    margin: 2,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  winningSquare: {
    backgroundColor: COLORS.accent,
    borderColor: COLORS.secondary,
  },
  squareText: {
    fontSize: 36,
    fontFamily: 'Fredoka-Regular',
  },
  xText: {
    color: COLORS.secondary,
  },
  oText: {
    color: COLORS.primary,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginTop: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  resetButtonText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 10,
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
  },
  instructionsTitle: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    lineHeight: 20,
  },
});