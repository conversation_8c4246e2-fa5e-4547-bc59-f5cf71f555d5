# 📱 Guia de Testes - Kids Game Room

## 🚀 Como Testar em Múltiplos Dispositivos

### Pré-requisitos
1. **Expo Go** instalado nos dispositivos de teste
2. **Mesma rede Wi-Fi** para todos os dispositivos
3. **Permissões** de câmera e microfone habilitadas

### Passos para Teste

#### 1. Iniciar o Servidor de Desenvolvimento
```bash
# Opção 1: Rede local (mais rápido)
npm run dev:lan

# Opção 2: Túnel (funciona em qualquer lugar)
npm run dev:tunnel

# Opção 3: Teste completo
npm run test
```

#### 2. Conectar Dispositivos
1. Abra o **Expo Go** em cada dispositivo
2. Escaneie o QR code que aparece no terminal
3. Aguarde o app carregar

#### 3. Testar Funcionalidades Multiplayer

##### Teste 1: Criação e Entrada em Salas
- **Dispositivo 1**: Criar nova sala
- **Dispositivo 2**: Entrar na sala usando o código
- **Verificar**: Ambos aparecem na lista de jogadores

##### Teste 2: Sincronização em Tempo Real
- **Dispositivo 1**: Fazer uma jogada no Jogo da Velha
- **Dispositivo 2**: Verificar se a jogada aparece instantaneamente
- **Alternar**: Cada dispositivo faz uma jogada

##### Teste 3: Chat de Vídeo
- **Ambos dispositivos**: Ir para aba "Video Chat"
- **Permitir**: Acesso à câmera e microfone
- **Testar**: Ligar/desligar câmera e áudio
- **Verificar**: Se conseguem se ver e ouvir

##### Teste 4: Gerenciamento de Sala
- **Host**: Expulsar um jogador
- **Jogador**: Sair da sala voluntariamente
- **Verificar**: Atualizações em tempo real

### 🔧 Configurações de Rede

#### Para Rede Local (LAN)
```bash
npm run dev:lan
```
- Mais rápido
- Todos os dispositivos devem estar na mesma rede Wi-Fi
- IP será algo como: `exp://*************:8081`

#### Para Túnel (Internet)
```bash
npm run dev:tunnel
```
- Funciona de qualquer lugar
- Mais lento, mas mais confiável
- URL será algo como: `exp://abc123.tunnel.exp.direct:80`

### 📱 Testando em Diferentes Plataformas

#### Android
- Use **Expo Go** da Play Store
- Permissões são solicitadas automaticamente
- Teste especialmente o áudio/vídeo

#### iOS
- Use **Expo Go** da App Store
- Permissões podem precisar ser habilitadas manualmente
- Vá em Configurações > Privacidade se necessário

#### Web (Desenvolvimento)
```bash
npm run start
# Pressione 'w' para abrir no navegador
```

### 🐛 Solução de Problemas

#### Problema: "Não consegue conectar"
- Verifique se todos estão na mesma rede Wi-Fi
- Tente usar o modo túnel: `npm run dev:tunnel`
- Reinicie o servidor e reconecte

#### Problema: "Câmera não funciona"
- Verifique permissões no dispositivo
- Feche outros apps que usam câmera
- Reinicie o app

#### Problema: "Jogadas não sincronizam"
- Verifique conexão com internet
- Reinicie o app em ambos dispositivos
- Verifique se o Firebase está configurado

#### Problema: "Sala não encontrada"
- Verifique se o código está correto (6 caracteres)
- Certifique-se que o host não saiu da sala
- Tente criar uma nova sala

### 📊 Cenários de Teste Recomendados

#### Cenário 1: Família Básica (2 pessoas)
- 1 adulto + 1 criança
- Testar criação de sala e jogo da velha
- Testar chat de vídeo básico

#### Cenário 2: Grupo Pequeno (3 pessoas)
- Testar entrada de terceira pessoa
- Verificar sincronização com 3 dispositivos
- Testar saída de jogador não-host

#### Cenário 3: Sala Cheia (4 pessoas)
- Testar limite máximo de jogadores
- Verificar performance com 4 câmeras
- Testar expulsão de jogadores

#### Cenário 4: Reconexão
- Simular perda de conexão
- Testar reconexão automática
- Verificar estado da sala após reconexão

### 🎯 Métricas de Sucesso

#### Funcionalidade Básica
- [ ] Criação de sala funciona
- [ ] Entrada em sala funciona
- [ ] Lista de jogadores atualiza em tempo real
- [ ] Jogo da velha sincroniza entre dispositivos

#### Áudio/Vídeo
- [ ] Câmera liga/desliga
- [ ] Microfone liga/desliga
- [ ] Troca de câmera (frente/traseira)
- [ ] Qualidade de vídeo aceitável

#### Experiência do Usuário
- [ ] Interface intuitiva para crianças
- [ ] Feedback visual claro
- [ ] Sem travamentos ou erros
- [ ] Performance fluida

### 📝 Log de Testes

Use esta seção para anotar resultados dos testes:

**Data**: ___________
**Dispositivos**: ___________
**Cenário**: ___________
**Resultado**: ___________
**Problemas encontrados**: ___________
**Soluções aplicadas**: ___________

---

## 🔥 Dicas para Desenvolvimento

1. **Use o modo túnel** para testes com pessoas em locais diferentes
2. **Mantenha o console aberto** para ver logs de erro
3. **Teste sempre com dispositivos reais**, não apenas simulador
4. **Documente bugs** encontrados durante os testes
5. **Teste com crianças reais** para validar UX
