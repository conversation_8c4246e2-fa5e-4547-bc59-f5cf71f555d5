/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/drawing` | `/drawing`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/math` | `/math`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/memory` | `/memory`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/painting` | `/painting`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tic-tac-toe` | `/tic-tac-toe`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/video-chat` | `/video-chat`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/word-search` | `/word-search`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/drawing` | `/drawing`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/math` | `/math`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/memory` | `/memory`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/painting` | `/painting`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/tic-tac-toe` | `/tic-tac-toe`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/video-chat` | `/video-chat`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/word-search` | `/word-search`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/drawing${`?${string}` | `#${string}` | ''}` | `/drawing${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/math${`?${string}` | `#${string}` | ''}` | `/math${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/memory${`?${string}` | `#${string}` | ''}` | `/memory${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/painting${`?${string}` | `#${string}` | ''}` | `/painting${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/tic-tac-toe${`?${string}` | `#${string}` | ''}` | `/tic-tac-toe${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/video-chat${`?${string}` | `#${string}` | ''}` | `/video-chat${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/word-search${`?${string}` | `#${string}` | ''}` | `/word-search${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/drawing` | `/drawing`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/math` | `/math`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/memory` | `/memory`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/painting` | `/painting`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tic-tac-toe` | `/tic-tac-toe`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/video-chat` | `/video-chat`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/word-search` | `/word-search`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
