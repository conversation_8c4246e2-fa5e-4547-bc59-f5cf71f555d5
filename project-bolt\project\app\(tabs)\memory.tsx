import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RotateCcw, Trophy, Users, Timer } from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

const EMOJIS = ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮'];

interface Card {
  id: number;
  emoji: string;
  isFlipped: boolean;
  isMatched: boolean;
}

const { width } = Dimensions.get('window');
const cardSize = (width - 60) / 4 - 10;

export default function MemoryGameScreen() {
  const { currentRoom } = useGameRoom();
  const [cards, setCards] = useState<Card[]>([]);
  const [flippedCards, setFlippedCards] = useState<number[]>([]);
  const [matchedPairs, setMatchedPairs] = useState<number>(0);
  const [moves, setMoves] = useState<number>(0);
  const [gameWon, setGameWon] = useState<boolean>(false);
  const [timeElapsed, setTimeElapsed] = useState<number>(0);
  const [gameStarted, setGameStarted] = useState<boolean>(false);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (gameStarted && !gameWon) {
      interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [gameStarted, gameWon]);

  // Initialize game
  useEffect(() => {
    initializeGame();
  }, []);

  const initializeGame = () => {
    const selectedEmojis = EMOJIS.slice(0, 8);
    const gameEmojis = [...selectedEmojis, ...selectedEmojis];
    
    // Shuffle array
    for (let i = gameEmojis.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [gameEmojis[i], gameEmojis[j]] = [gameEmojis[j], gameEmojis[i]];
    }

    const initialCards: Card[] = gameEmojis.map((emoji, index) => ({
      id: index,
      emoji,
      isFlipped: false,
      isMatched: false,
    }));

    setCards(initialCards);
    setFlippedCards([]);
    setMatchedPairs(0);
    setMoves(0);
    setGameWon(false);
    setTimeElapsed(0);
    setGameStarted(false);
  };

  const handleCardPress = (cardId: number) => {
    if (!gameStarted) {
      setGameStarted(true);
    }

    const card = cards.find(c => c.id === cardId);
    if (!card || card.isFlipped || card.isMatched || flippedCards.length >= 2) {
      return;
    }

    const newFlippedCards = [...flippedCards, cardId];
    setFlippedCards(newFlippedCards);

    // Update card state
    setCards(prevCards => 
      prevCards.map(c => 
        c.id === cardId ? { ...c, isFlipped: true } : c
      )
    );

    if (newFlippedCards.length === 2) {
      setMoves(prev => prev + 1);
      
      const [firstCardId, secondCardId] = newFlippedCards;
      const firstCard = cards.find(c => c.id === firstCardId);
      const secondCard = cards.find(c => c.id === secondCardId);

      if (firstCard && secondCard && firstCard.emoji === secondCard.emoji) {
        // Match found!
        setTimeout(() => {
          setCards(prevCards => 
            prevCards.map(c => 
              c.id === firstCardId || c.id === secondCardId 
                ? { ...c, isMatched: true } 
                : c
            )
          );
          setMatchedPairs(prev => prev + 1);
          setFlippedCards([]);
          
          // Check if game is won
          if (matchedPairs + 1 === 8) {
            setGameWon(true);
            Alert.alert(
              '🎉 Parabéns!',
              `Vocês completaram o jogo!\nTempo: ${formatTime(timeElapsed)}\nMovimentos: ${moves + 1}`,
              [{ text: 'Jogar Novamente', onPress: initializeGame }]
            );
          }
        }, 1000);
      } else {
        // No match, flip cards back
        setTimeout(() => {
          setCards(prevCards => 
            prevCards.map(c => 
              c.id === firstCardId || c.id === secondCardId 
                ? { ...c, isFlipped: false } 
                : c
            )
          );
          setFlippedCards([]);
        }, 1000);
      }
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderCard = (card: Card) => {
    const showEmoji = card.isFlipped || card.isMatched;
    
    return (
      <TouchableOpacity
        key={card.id}
        style={[
          styles.card,
          showEmoji && styles.flippedCard,
          card.isMatched && styles.matchedCard,
        ]}
        onPress={() => handleCardPress(card.id)}
        disabled={card.isFlipped || card.isMatched}
      >
        <Text style={styles.cardText}>
          {showEmoji ? card.emoji : '?'}
        </Text>
      </TouchableOpacity>
    );
  };

  if (!currentRoom) {
    return (
      <View style={styles.noRoomContainer}>
        <Users size={80} color={COLORS.primary} />
        <Text style={styles.noRoomText}>
          Você precisa estar em uma sala para jogar!
        </Text>
        <Text style={styles.noRoomSubtext}>
          Vá para a tela inicial e crie ou entre em uma sala.
        </Text>
      </View>
    );
  }

  return (
    <LinearGradient
      colors={['#FF9A9E', '#FECFEF']}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Jogo da Memória</Text>
        <View style={styles.playersInfo}>
          <Text style={styles.playersText}>
            {currentRoom.players.map(p => p.name).join(' e ')} jogando juntos!
          </Text>
          <Text style={styles.roomText}>Sala: {currentRoom.id}</Text>
        </View>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Timer size={20} color="white" />
          <Text style={styles.statText}>{formatTime(timeElapsed)}</Text>
        </View>
        <View style={styles.statItem}>
          <Trophy size={20} color="white" />
          <Text style={styles.statText}>{matchedPairs}/8</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Movimentos:</Text>
          <Text style={styles.statText}>{moves}</Text>
        </View>
      </View>

      <View style={styles.gameContainer}>
        <View style={styles.grid}>
          {cards.map(card => renderCard(card))}
        </View>
      </View>

      <TouchableOpacity
        style={styles.resetButton}
        onPress={initializeGame}
      >
        <RotateCcw size={24} color="white" />
        <Text style={styles.resetButtonText}>Novo Jogo</Text>
      </TouchableOpacity>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Como Jogar:</Text>
        <Text style={styles.instructionsText}>
          • Toquem nas cartas para virá-las{'\n'}
          • Encontrem os pares iguais{'\n'}
          • Trabalhem juntos para encontrar todos os pares!{'\n'}
          • Quanto menos movimentos, melhor!
        </Text>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  noRoomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  noRoomText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  noRoomSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  playersInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
  },
  playersText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 5,
  },
  roomText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    marginRight: 5,
  },
  statText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 5,
  },
  gameContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    maxWidth: width - 40,
  },
  card: {
    width: cardSize,
    height: cardSize,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    margin: 5,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  flippedCard: {
    backgroundColor: 'white',
  },
  matchedCard: {
    backgroundColor: COLORS.success,
    transform: [{ scale: 0.95 }],
  },
  cardText: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginTop: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  resetButtonText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 10,
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    padding: 15,
    marginTop: 10,
  },
  instructionsTitle: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    lineHeight: 18,
  },
});