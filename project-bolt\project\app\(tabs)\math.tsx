import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  RotateCcw, 
  Trophy, 
  Users, 
  Timer, 
  Plus, 
  Minus, 
  X,
  Star
} from 'lucide-react-native';
import { useGameRoom } from '@/hooks/useGameRoom';

const COLORS = {
  primary: '#4ECDC4',
  secondary: '#FF6B9D',
  accent: '#FFE066',
  success: '#95E1D3',
  background: '#F8F9FA',
  text: '#2C3E50'
};

type MathOperation = 'addition' | 'subtraction' | 'multiplication';
type Difficulty = 'easy' | 'medium' | 'hard';

interface MathProblem {
  question: string;
  answer: number;
  num1: number;
  num2: number;
  operation: MathOperation;
}

export default function MathGameScreen() {
  const { currentRoom } = useGameRoom();
  const [currentProblem, setCurrentProblem] = useState<MathProblem | null>(null);
  const [userAnswer, setUserAnswer] = useState<string>('');
  const [score, setScore] = useState<number>(0);
  const [streak, setStreak] = useState<number>(0);
  const [timeRemaining, setTimeRemaining] = useState<number>(30);
  const [isGameActive, setIsGameActive] = useState<boolean>(false);
  const [difficulty, setDifficulty] = useState<Difficulty>('easy');
  const [operation, setOperation] = useState<MathOperation>('addition');

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isGameActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            endGame();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isGameActive, timeRemaining]);

  const generateProblem = (): MathProblem => {
    let num1: number, num2: number, answer: number, question: string;

    const ranges = {
      easy: { min: 1, max: 10 },
      medium: { min: 5, max: 25 },
      hard: { min: 10, max: 50 },
    };

    const range = ranges[difficulty];

    switch (operation) {
      case 'addition':
        num1 = Math.floor(Math.random() * range.max) + range.min;
        num2 = Math.floor(Math.random() * range.max) + range.min;
        answer = num1 + num2;
        question = `${num1} + ${num2} = ?`;
        break;
      
      case 'subtraction':
        num1 = Math.floor(Math.random() * range.max) + range.min;
        num2 = Math.floor(Math.random() * num1) + 1; // Ensure positive result
        answer = num1 - num2;
        question = `${num1} - ${num2} = ?`;
        break;
      
      case 'multiplication':
        const multiRange = difficulty === 'easy' ? 5 : difficulty === 'medium' ? 10 : 12;
        num1 = Math.floor(Math.random() * multiRange) + 1;
        num2 = Math.floor(Math.random() * multiRange) + 1;
        answer = num1 * num2;
        question = `${num1} × ${num2} = ?`;
        break;
      
      default:
        num1 = 1; num2 = 1; answer = 2; question = '1 + 1 = ?';
    }

    return { question, answer, num1, num2, operation };
  };

  const startGame = () => {
    setIsGameActive(true);
    setScore(0);
    setStreak(0);
    setTimeRemaining(60); // 1 minute game
    setCurrentProblem(generateProblem());
    setUserAnswer('');
  };

  const endGame = () => {
    setIsGameActive(false);
    Alert.alert(
      '🎉 Fim do Jogo!',
      `Pontuação final: ${score} pontos\nSequência máxima: ${streak} acertos`,
      [{ text: 'Jogar Novamente', onPress: startGame }]
    );
  };

  const checkAnswer = () => {
    if (!currentProblem || !userAnswer.trim()) return;

    const answer = parseInt(userAnswer);
    
    if (answer === currentProblem.answer) {
      // Correct answer
      const points = difficulty === 'easy' ? 10 : difficulty === 'medium' ? 20 : 30;
      const streakBonus = Math.floor(streak / 3) * 5; // Bonus every 3 correct answers
      const totalPoints = points + streakBonus;
      
      setScore(prev => prev + totalPoints);
      setStreak(prev => prev + 1);
      
      Alert.alert('🎉 Correto!', `+${totalPoints} pontos!`);
    } else {
      // Wrong answer
      setStreak(0);
      Alert.alert(
        '❌ Incorreto',
        `A resposta correta era ${currentProblem.answer}`
      );
    }

    // Generate next problem
    setCurrentProblem(generateProblem());
    setUserAnswer('');
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getOperationIcon = (op: MathOperation) => {
    switch (op) {
      case 'addition': return <Plus size={24} color="white" />;
      case 'subtraction': return <Minus size={24} color="white" />;
      case 'multiplication': return <X size={24} color="white" />;
    }
  };

  const getOperationName = (op: MathOperation): string => {
    switch (op) {
      case 'addition': return 'Soma';
      case 'subtraction': return 'Subtração';
      case 'multiplication': return 'Multiplicação';
    }
  };

  if (!currentRoom) {
    return (
      <View style={styles.noRoomContainer}>
        <Users size={80} color={COLORS.primary} />
        <Text style={styles.noRoomText}>
          Você precisa estar em uma sala para jogar!
        </Text>
        <Text style={styles.noRoomSubtext}>
          Vá para a tela inicial e crie ou entre em uma sala.
        </Text>
      </View>
    );
  }

  if (!isGameActive) {
    return (
      <LinearGradient
        colors={['#FFB75E', '#ED8F03']}
        style={styles.container}
      >
        <View style={styles.setupContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Jogo de Matemática</Text>
            <View style={styles.playersInfo}>
              <Text style={styles.playersText}>
                {currentRoom.players.map(p => p.name).join(' e ')} vão praticar matemática!
              </Text>
              <Text style={styles.roomText}>Sala: {currentRoom.id}</Text>
            </View>
          </View>

          <View style={styles.setupSection}>
            <Text style={styles.setupTitle}>Escolha a Dificuldade:</Text>
            <View style={styles.optionsContainer}>
              {(['easy', 'medium', 'hard'] as Difficulty[]).map(level => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.optionButton,
                    difficulty === level && styles.activeOptionButton
                  ]}
                  onPress={() => setDifficulty(level)}
                >
                  <Text style={[
                    styles.optionButtonText,
                    difficulty === level && styles.activeOptionButtonText
                  ]}>
                    {level === 'easy' ? 'Fácil (1-10)' : 
                     level === 'medium' ? 'Médio (5-25)' : 'Difícil (10-50)'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.setupSection}>
            <Text style={styles.setupTitle}>Escolha a Operação:</Text>
            <View style={styles.optionsContainer}>
              {(['addition', 'subtraction', 'multiplication'] as MathOperation[]).map(op => (
                <TouchableOpacity
                  key={op}
                  style={[
                    styles.operationButton,
                    operation === op && styles.activeOperationButton
                  ]}
                  onPress={() => setOperation(op)}
                >
                  {getOperationIcon(op)}
                  <Text style={[
                    styles.operationButtonText,
                    operation === op && styles.activeOperationButtonText
                  ]}>
                    {getOperationName(op)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <TouchableOpacity
            style={styles.startButton}
            onPress={startGame}
          >
            <Trophy size={32} color="white" />
            <Text style={styles.startButtonText}>Começar Jogo!</Text>
          </TouchableOpacity>

          <View style={styles.instructions}>
            <Text style={styles.instructionsTitle}>Como Jogar:</Text>
            <Text style={styles.instructionsText}>
              • Respondam às questões o mais rápido possível{'\n'}
              • Ganhem pontos por cada resposta correta{'\n'}
              • Façam sequências para ganhar bônus{'\n'}
              • Vocês têm 1 minuto para fazer o máximo de pontos!
            </Text>
          </View>
        </View>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#FFB75E', '#ED8F03']}
      style={styles.container}
    >
      <View style={styles.gameContainer}>
        <View style={styles.gameHeader}>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Timer size={20} color="white" />
              <Text style={styles.statText}>{formatTime(timeRemaining)}</Text>
            </View>
            <View style={styles.statItem}>
              <Trophy size={20} color="white" />
              <Text style={styles.statText}>{score}</Text>
            </View>
            <View style={styles.statItem}>
              <Star size={20} color="white" />
              <Text style={styles.statText}>x{streak}</Text>
            </View>
          </View>
        </View>

        {currentProblem && (
          <View style={styles.problemContainer}>
            <Text style={styles.problemText}>{currentProblem.question}</Text>
            
            <View style={styles.answerContainer}>
              <TextInput
                style={styles.answerInput}
                value={userAnswer}
                onChangeText={setUserAnswer}
                placeholder="Sua resposta"
                placeholderTextColor="#7F8C8D"
                keyboardType="numeric"
                autoFocus
                onSubmitEditing={checkAnswer}
              />
              
              <TouchableOpacity
                style={styles.submitButton}
                onPress={checkAnswer}
                disabled={!userAnswer.trim()}
              >
                <Text style={styles.submitButtonText}>✓</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <View style={styles.difficultyIndicator}>
          <Text style={styles.difficultyText}>
            {getOperationName(operation)} - {
              difficulty === 'easy' ? 'Fácil' : 
              difficulty === 'medium' ? 'Médio' : 'Difícil'
            }
          </Text>
        </View>

        <TouchableOpacity
          style={styles.endGameButton}
          onPress={endGame}
        >
          <Text style={styles.endGameButtonText}>Terminar Jogo</Text>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
  },
  noRoomContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: COLORS.background,
  },
  noRoomText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  noRoomSubtext: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Regular',
    color: '#7F8C8D',
    textAlign: 'center',
  },
  setupContainer: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  playersInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
  },
  playersText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 5,
  },
  roomText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    opacity: 0.8,
  },
  setupSection: {
    marginBottom: 25,
  },
  setupTitle: {
    fontSize: 20,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 15,
  },
  optionsContainer: {
    alignItems: 'center',
  },
  optionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    marginBottom: 10,
    minWidth: 250,
  },
  activeOptionButton: {
    backgroundColor: 'white',
  },
  optionButtonText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
  },
  activeOptionButtonText: {
    color: COLORS.text,
  },
  operationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    marginBottom: 10,
    minWidth: 200,
  },
  activeOperationButton: {
    backgroundColor: 'white',
  },
  operationButtonText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 10,
  },
  activeOperationButtonText: {
    color: COLORS.text,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 40,
    paddingVertical: 20,
    borderRadius: 30,
    marginVertical: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  startButtonText: {
    fontSize: 24,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    marginLeft: 15,
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    padding: 20,
  },
  instructionsTitle: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'ComicNeue-Regular',
    color: 'white',
    lineHeight: 20,
  },
  gameContainer: {
    flex: 1,
  },
  gameHeader: {
    marginBottom: 40,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    padding: 20,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 18,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    marginLeft: 8,
  },
  problemContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  problemText: {
    fontSize: 48,
    fontFamily: 'Fredoka-Regular',
    color: 'white',
    textAlign: 'center',
    marginBottom: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 30,
    paddingVertical: 20,
    borderRadius: 20,
  },
  answerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 40,
  },
  answerInput: {
    backgroundColor: 'white',
    fontSize: 24,
    fontFamily: 'ComicNeue-Bold',
    color: COLORS.text,
    textAlign: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 15,
    minWidth: 120,
    marginRight: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  submitButton: {
    backgroundColor: COLORS.success,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  submitButtonText: {
    fontSize: 28,
    color: 'white',
  },
  difficultyIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
  },
  difficultyText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
    textAlign: 'center',
  },
  endGameButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    alignSelf: 'center',
  },
  endGameButtonText: {
    fontSize: 16,
    fontFamily: 'ComicNeue-Bold',
    color: 'white',
  },
});