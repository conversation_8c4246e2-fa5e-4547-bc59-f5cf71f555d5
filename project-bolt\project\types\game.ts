export interface GameRoom {
  id: string;
  players: Player[];
  currentGame: GameType;
  isActive: boolean;
  createdAt: Date;
}

export interface Player {
  id: string;
  name: string;
  avatar?: string;
  isHost: boolean;
}

export type GameType = 
  | 'tic-tac-toe' 
  | 'memory' 
  | 'word-search' 
  | 'math' 
  | 'drawing' 
  | 'painting';

export interface TicTacToeGame {
  board: (string | null)[];
  currentPlayer: string;
  winner: string | null;
  gameOver: boolean;
}

export interface MemoryGame {
  cards: MemoryCard[];
  flippedCards: number[];
  matchedPairs: number[];
  currentPlayer: string;
  moves: number;
}

export interface MemoryCard {
  id: number;
  emoji: string;
  isFlipped: boolean;
  isMatched: boolean;
}

export interface WordSearchGame {
  grid: string[][];
  words: string[];
  foundWords: string[];
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface MathGame {
  currentProblem: MathProblem;
  score: number;
  level: number;
  timeRemaining: number;
}

export interface MathProblem {
  question: string;
  answer: number;
  options?: number[];
  type: 'addition' | 'subtraction' | 'multiplication';
}

export interface DrawingData {
  strokes: DrawingStroke[];
  currentTool: DrawingTool;
  backgroundColor: string;
}

export interface DrawingStroke {
  points: Point[];
  color: string;
  width: number;
  tool: DrawingTool;
}

export interface Point {
  x: number;
  y: number;
}

export type DrawingTool = 'pen' | 'brush' | 'eraser' | 'highlighter';